/**
 * 🚀 ENHANCED CRM DASHBOARD
 * 
 * Advanced CRM dashboard combining Customer Lifecycle Management
 * and Service Excellence features in one powerful interface.
 * 
 * Philosophy: "The dashboard is the window to the soul of our business"
 */

import { json, type LoaderFunctionArgs, type MetaFunction } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { CustomerLifecycleDashboard } from "~/components/organisms/CustomerLifecycleDashboard";
import { ServiceExcellenceDashboard } from "~/components/organisms/ServiceExcellenceDashboard";
import { 
  Users, 
  Target, 
  TrendingUp, 
  Award,
  Heart,
  Wrench,
  Bar<PERSON>hart3,
  Settings,
  RefreshCw,
  Download,
  Calendar,
  AlertTriangle
} from "lucide-react";

export const meta: MetaFunction = () => {
  return [
    { title: "Enhanced CRM Dashboard - HVAC Servicetool" },
    { name: "description", content: "Advanced customer lifecycle and service excellence management dashboard" },
  ];
};

interface DashboardMetrics {
  customerLifecycle: {
    totalCustomers: number;
    averageHealthScore: number;
    atRiskCustomers: number;
    loyalCustomers: number;
    churnRate: number;
  };
  serviceExcellence: {
    slaComplianceRate: number;
    averageResponseTime: number;
    customerSatisfaction: number;
    firstTimeFixRate: number;
    criticalBreaches: number;
  };
  businessMetrics: {
    totalRevenue: number;
    monthlyGrowth: number;
    activeContracts: number;
    pendingOrders: number;
  };
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // In a real implementation, these would be actual API calls
    // For now, providing realistic sample data
    const dashboardMetrics: DashboardMetrics = {
      customerLifecycle: {
        totalCustomers: 1247,
        averageHealthScore: 82,
        atRiskCustomers: 23,
        loyalCustomers: 156,
        churnRate: 3.2
      },
      serviceExcellence: {
        slaComplianceRate: 94.5,
        averageResponseTime: 3.2,
        customerSatisfaction: 4.6,
        firstTimeFixRate: 87.3,
        criticalBreaches: 2
      },
      businessMetrics: {
        totalRevenue: 2847650,
        monthlyGrowth: 12.5,
        activeContracts: 89,
        pendingOrders: 34
      }
    };

    return json({ metrics: dashboardMetrics });
  } catch (error) {
    console.error('Error loading dashboard data:', error);
    return json(
      { error: 'Failed to load dashboard data' },
      { status: 500 }
    );
  }
}

export default function EnhancedCRMDashboard() {
  const { metrics } = useLoaderData<typeof loader>();
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 2000);
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSLAComplianceColor = (rate: number): string => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 85) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enhanced CRM Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Advanced customer lifecycle and service excellence management
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold">{metrics.customerLifecycle.totalCustomers.toLocaleString()}</p>
                <p className="text-sm text-green-600 mt-1">
                  +{metrics.businessMetrics.monthlyGrowth}% this month
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Health Score</p>
                <p className={`text-2xl font-bold ${getHealthScoreColor(metrics.customerLifecycle.averageHealthScore)}`}>
                  {metrics.customerLifecycle.averageHealthScore}%
                </p>
                <p className="text-sm text-gray-600 mt-1">Customer health</p>
              </div>
              <Heart className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">SLA Compliance</p>
                <p className={`text-2xl font-bold ${getSLAComplianceColor(metrics.serviceExcellence.slaComplianceRate)}`}>
                  {metrics.serviceExcellence.slaComplianceRate}%
                </p>
                <p className="text-sm text-gray-600 mt-1">Service level</p>
              </div>
              <Target className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  ${(metrics.businessMetrics.totalRevenue / 1000000).toFixed(1)}M
                </p>
                <p className="text-sm text-green-600 mt-1">
                  +{metrics.businessMetrics.monthlyGrowth}% growth
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alert Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {metrics.customerLifecycle.atRiskCustomers > 0 && (
          <Card className="border-l-4 border-l-red-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="font-medium text-red-800">
                    {metrics.customerLifecycle.atRiskCustomers} At-Risk Customers
                  </p>
                  <p className="text-sm text-red-600">Require immediate attention</p>
                </div>
                <Button size="sm" variant="outline" className="ml-auto">
                  Review
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {metrics.serviceExcellence.criticalBreaches > 0 && (
          <Card className="border-l-4 border-l-orange-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-800">
                    {metrics.serviceExcellence.criticalBreaches} SLA Breaches
                  </p>
                  <p className="text-sm text-orange-600">Critical priority orders</p>
                </div>
                <Button size="sm" variant="outline" className="ml-auto">
                  Fix Now
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {metrics.businessMetrics.pendingOrders > 0 && (
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">
                    {metrics.businessMetrics.pendingOrders} Pending Orders
                  </p>
                  <p className="text-sm text-blue-600">Awaiting scheduling</p>
                </div>
                <Button size="sm" variant="outline" className="ml-auto">
                  Schedule
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="customer-lifecycle" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="customer-lifecycle" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customer Lifecycle
          </TabsTrigger>
          <TabsTrigger value="service-excellence" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            Service Excellence
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="customer-lifecycle" className="mt-6">
          <CustomerLifecycleDashboard showOverview={true} />
        </TabsContent>

        <TabsContent value="service-excellence" className="mt-6">
          <ServiceExcellenceDashboard timeRange="month" />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Advanced Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Customer Insights</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Loyal Customers:</span>
                      <Badge variant="outline">{metrics.customerLifecycle.loyalCustomers}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Churn Rate:</span>
                      <Badge variant={metrics.customerLifecycle.churnRate > 5 ? "destructive" : "secondary"}>
                        {metrics.customerLifecycle.churnRate}%
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Customer Satisfaction:</span>
                      <Badge variant="outline">{metrics.serviceExcellence.customerSatisfaction}/5.0</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Service Performance</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>First Time Fix Rate:</span>
                      <Badge variant="outline">{metrics.serviceExcellence.firstTimeFixRate}%</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Response Time:</span>
                      <Badge variant="outline">{metrics.serviceExcellence.averageResponseTime}h</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Contracts:</span>
                      <Badge variant="outline">{metrics.businessMetrics.activeContracts}</Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Quick Actions</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link to="/customers">Manage Customers</Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <Link to="/service-orders">Service Orders</Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <Link to="/reports">Generate Reports</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
