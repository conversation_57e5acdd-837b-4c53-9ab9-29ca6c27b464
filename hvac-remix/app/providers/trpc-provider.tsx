/**
 * tRPC Provider for HVAC-Remix
 *
 * Provides tRPC client and React Query integration for the entire app
 * Enables type-safe communication with GoBackend-Kratos
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { useState, type ReactNode } from 'react';

// Simple router type for testing
type SimpleRouter = {
  system: {
    health: {
      query: () => Promise<{ status: string; services: Record<string, boolean> }>;
    };
  };
  customer: {
    list: {
      query: (params?: { limit?: number; page?: number }) => Promise<{ data: any[]; total: number }>;
    };
  };
};

// Create tRPC React instance
export const trpc = createTRPCReact<SimpleRouter>();

// Environment configuration
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Browser should use the backend URL directly
    return 'http://localhost:8081';
  }
  // SSR should use absolute URL
  return process.env.GOBACKEND_URL || 'http://localhost:8081';
};

const getWsUrl = () => {
  if (typeof window !== 'undefined') {
    // Browser WebSocket URL - point to backend
    return 'ws://localhost:8081';
  }
  // SSR WebSocket URL
  return process.env.GOBACKEND_WS_URL || 'ws://localhost:8081';
};

// Authentication token management
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
}

interface TRPCProviderProps {
  children: ReactNode;
}

export function TRPCProvider({ children }: TRPCProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // With SSR, we usually want to set some default staleTime
            // above 0 to avoid refetching immediately on the client
            staleTime: 60 * 1000, // 1 minute
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              // Retry up to 3 times for other errors
              return failureCount < 3;
            },
          },
          mutations: {
            retry: false,
          },
        },
      })
  );

  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        httpBatchLink({
          url: `${getBaseUrl()}/api/trpc`,
          headers: () => {
            const token = getAuthToken();
            return {
              authorization: token ? `Bearer ${token}` : '',
              'content-type': 'application/json',
            };
          },
        }),
      ],
    })
  );

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
        {/* Show React Query DevTools in development */}
        {/* {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )} */}
      </QueryClientProvider>
    </trpc.Provider>
  );
}

// Export the trpc instance for use in components
export { trpc };

// Health check utility
export async function checkGoBackendHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  latency: number;
}> {
  const startTime = Date.now();

  try {
    const response = await fetch(`${getBaseUrl()}/health`);
    const latency = Date.now() - startTime;

    if (response.ok) {
      return { status: 'healthy', latency };
    } else {
      return { status: 'unhealthy', latency };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      latency: Date.now() - startTime,
    };
  }
}

// Error boundary for tRPC errors
export function TRPCErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <div>
      {children}
    </div>
  );
}
