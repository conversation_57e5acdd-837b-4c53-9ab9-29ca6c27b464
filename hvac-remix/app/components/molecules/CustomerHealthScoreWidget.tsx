/**
 * 💚 CUSTOMER HEALTH SCORE WIDGET
 * 
 * Interactive widget displaying customer health metrics with
 * visual indicators and actionable insights.
 * 
 * Philosophy: "Health is not just a number, it's a story of relationship"
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { 
  Heart, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle,
  Info,
  Phone,
  Mail,
  Calendar
} from 'lucide-react';
import { CustomerHealthMetrics } from '~/models/customer-lifecycle.server';

interface CustomerHealthScoreWidgetProps {
  customerId: string;
  customerName: string;
  healthMetrics: CustomerHealthMetrics;
  lastUpdated?: Date;
  onActionClick?: (action: string) => void;
  compact?: boolean;
}

export function CustomerHealthScoreWidget({
  customerId,
  customerName,
  healthMetrics,
  lastUpdated,
  onActionClick,
  compact = false
}: CustomerHealthScoreWidgetProps) {
  const [showDetails, setShowDetails] = useState(false);

  const getHealthColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthBadgeColor = (score: number): string => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getHealthStatus = (score: number): string => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 60) return 'Fair';
    if (score >= 40) return 'Poor';
    return 'Critical';
  };

  const getHealthIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 60) return <Info className="h-5 w-5 text-yellow-600" />;
    return <AlertTriangle className="h-5 w-5 text-red-600" />;
  };

  const getRecommendedActions = (metrics: CustomerHealthMetrics): Array<{
    action: string;
    label: string;
    icon: React.ReactNode;
    priority: 'high' | 'medium' | 'low';
  }> => {
    const actions = [];

    if (metrics.paymentHistory < 80) {
      actions.push({
        action: 'review_payment_history',
        label: 'Review Payment Issues',
        icon: <AlertTriangle className="h-4 w-4" />,
        priority: 'high' as const
      });
    }

    if (metrics.serviceFrequency < 60) {
      actions.push({
        action: 'schedule_follow_up',
        label: 'Schedule Follow-up',
        icon: <Calendar className="h-4 w-4" />,
        priority: 'high' as const
      });
    }

    if (metrics.communicationResponsiveness < 70) {
      actions.push({
        action: 'improve_communication',
        label: 'Improve Communication',
        icon: <Phone className="h-4 w-4" />,
        priority: 'medium' as const
      });
    }

    if (metrics.contractCompliance < 80) {
      actions.push({
        action: 'review_contract',
        label: 'Review Contract Terms',
        icon: <Info className="h-4 w-4" />,
        priority: 'medium' as const
      });
    }

    if (metrics.overallScore >= 80) {
      actions.push({
        action: 'upsell_opportunity',
        label: 'Explore Upsell',
        icon: <TrendingUp className="h-4 w-4" />,
        priority: 'low' as const
      });
    }

    return actions;
  };

  const recommendedActions = getRecommendedActions(healthMetrics);

  if (compact) {
    return (
      <Card className="w-full">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getHealthIcon(healthMetrics.overallScore)}
              <div>
                <p className="font-medium text-sm">{customerName}</p>
                <p className="text-xs text-gray-600">Health Score</p>
              </div>
            </div>
            <div className="text-right">
              <p className={`text-lg font-bold ${getHealthColor(healthMetrics.overallScore)}`}>
                {healthMetrics.overallScore}%
              </p>
              <Badge className={getHealthBadgeColor(healthMetrics.overallScore)} variant="secondary">
                {getHealthStatus(healthMetrics.overallScore)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-500" />
            Customer Health Score
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={getHealthBadgeColor(healthMetrics.overallScore)}>
              {getHealthStatus(healthMetrics.overallScore)}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </Button>
          </div>
        </div>
        <p className="text-sm text-gray-600">{customerName}</p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Overall Score */}
        <div className="text-center">
          <div className={`text-4xl font-bold ${getHealthColor(healthMetrics.overallScore)} mb-2`}>
            {healthMetrics.overallScore}%
          </div>
          <Progress value={healthMetrics.overallScore} className="h-3 mb-2" />
          <p className="text-sm text-gray-600">
            Overall Health Score
            {lastUpdated && (
              <span className="ml-2">
                • Updated {lastUpdated.toLocaleDateString()}
              </span>
            )}
          </p>
        </div>

        {/* Detailed Breakdown */}
        {showDetails && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-gray-700">Score Breakdown</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Service Frequency</span>
                  <span className={getHealthColor(healthMetrics.serviceFrequency)}>
                    {healthMetrics.serviceFrequency}%
                  </span>
                </div>
                <Progress value={healthMetrics.serviceFrequency} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Payment History</span>
                  <span className={getHealthColor(healthMetrics.paymentHistory)}>
                    {healthMetrics.paymentHistory}%
                  </span>
                </div>
                <Progress value={healthMetrics.paymentHistory} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Communication</span>
                  <span className={getHealthColor(healthMetrics.communicationResponsiveness)}>
                    {healthMetrics.communicationResponsiveness}%
                  </span>
                </div>
                <Progress value={healthMetrics.communicationResponsiveness} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Contract Compliance</span>
                  <span className={getHealthColor(healthMetrics.contractCompliance)}>
                    {healthMetrics.contractCompliance}%
                  </span>
                </div>
                <Progress value={healthMetrics.contractCompliance} className="h-2" />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Referral Activity</span>
                <span className={getHealthColor(healthMetrics.referralActivity)}>
                  {healthMetrics.referralActivity}%
                </span>
              </div>
              <Progress value={healthMetrics.referralActivity} className="h-2" />
            </div>
          </div>
        )}

        {/* Recommended Actions */}
        {recommendedActions.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-gray-700">Recommended Actions</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {recommendedActions.slice(0, 4).map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className={`justify-start h-auto p-3 ${
                    action.priority === 'high' 
                      ? 'border-red-200 hover:border-red-300' 
                      : action.priority === 'medium'
                      ? 'border-yellow-200 hover:border-yellow-300'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => onActionClick?.(action.action)}
                >
                  <div className="flex items-center gap-2">
                    {action.icon}
                    <span className="text-xs">{action.label}</span>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Health Trend Indicator */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span>Trending upward this month</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onActionClick?.('view_full_profile')}
          >
            View Full Profile
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
