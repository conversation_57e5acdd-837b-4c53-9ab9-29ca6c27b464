/**
 * 🏆 SERVICE EXCELLENCE DASHBOARD
 * 
 * Advanced dashboard for service excellence management with SLA tracking,
 * technician performance, and inventory optimization.
 * 
 * Philosophy: "Excellence is the gradual result of always striving to do better"
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { 
  Award, 
  Clock, 
  Target, 
  TrendingUp, 
  Users, 
  Wrench,
  Package,
  AlertTriangle,
  CheckCircle,
  Star,
  BarChart3,
  Calendar,
  DollarSign
} from 'lucide-react';
import { 
  TechnicianPerformanceMetrics,
  InventoryOptimizationRecommendation 
} from '~/models/service-excellence.server';

interface ServiceExcellenceDashboardProps {
  technicianId?: string;
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
}

interface SLAMetrics {
  totalServiceOrders: number;
  slaCompliantOrders: number;
  complianceRate: number;
  averageResponseTime: number;
  averageResolutionTime: number;
  criticalSLABreaches: number;
}

interface TeamPerformanceMetrics {
  totalTechnicians: number;
  averagePerformanceScore: number;
  topPerformer: string;
  totalRevenue: number;
  customerSatisfaction: number;
  firstTimeFixRate: number;
}

export function ServiceExcellenceDashboard({ 
  technicianId, 
  timeRange = 'month' 
}: ServiceExcellenceDashboardProps) {
  const [slaMetrics, setSlaMetrics] = useState<SLAMetrics | null>(null);
  const [teamMetrics, setTeamMetrics] = useState<TeamPerformanceMetrics | null>(null);
  const [technicianMetrics, setTechnicianMetrics] = useState<TechnicianPerformanceMetrics | null>(null);
  const [inventoryRecommendations, setInventoryRecommendations] = useState<InventoryOptimizationRecommendation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [technicianId, timeRange]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load SLA metrics
      const slaResponse = await fetch(`/api/service-excellence/sla-metrics?timeRange=${timeRange}`);
      const slaData = await slaResponse.json();
      setSlaMetrics(slaData);

      if (technicianId) {
        // Load specific technician metrics
        const techResponse = await fetch(`/api/service-excellence/technician/${technicianId}?timeRange=${timeRange}`);
        const techData = await techResponse.json();
        setTechnicianMetrics(techData);
      } else {
        // Load team overview metrics
        const teamResponse = await fetch(`/api/service-excellence/team-metrics?timeRange=${timeRange}`);
        const teamData = await teamResponse.json();
        setTeamMetrics(teamData);
      }

      // Load inventory recommendations
      const inventoryResponse = await fetch('/api/service-excellence/inventory-recommendations');
      const inventoryData = await inventoryResponse.json();
      setInventoryRecommendations(inventoryData);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSLAComplianceColor = (rate: number): string => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 85) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getUrgencyColor = (urgency: string): string => {
    switch (urgency) {
      case 'CRITICAL': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sla">SLA Tracking</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">SLA Compliance</p>
                    <p className={`text-2xl font-bold ${getSLAComplianceColor(slaMetrics?.complianceRate || 0)}`}>
                      {slaMetrics?.complianceRate || 0}%
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                    <p className="text-2xl font-bold">{slaMetrics?.averageResponseTime || 0}h</p>
                  </div>
                  <Clock className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Customer Satisfaction</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {teamMetrics?.customerSatisfaction || 0}%
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">First Time Fix</p>
                    <p className="text-2xl font-bold text-green-600">
                      {teamMetrics?.firstTimeFixRate || 0}%
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Team Performance Overview */}
          {teamMetrics && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Team Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Team Size</span>
                      <span className="text-lg font-bold">{teamMetrics.totalTechnicians}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Top Performer</span>
                      <Badge variant="outline">{teamMetrics.topPerformer}</Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Total Revenue</span>
                      <span className="text-lg font-bold text-green-600">
                        ${teamMetrics.totalRevenue.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Avg Performance</span>
                      <span className={`text-lg font-bold ${getPerformanceColor(teamMetrics.averagePerformanceScore)}`}>
                        {teamMetrics.averagePerformanceScore}%
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Button className="w-full" variant="outline">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      View Detailed Analytics
                    </Button>
                    <Button className="w-full" variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule Team Meeting
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="sla" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                SLA Performance Tracking
              </CardTitle>
            </CardHeader>
            <CardContent>
              {slaMetrics && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Service Orders</span>
                        <span className="text-lg font-bold">{slaMetrics.totalServiceOrders}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">SLA Compliant</span>
                        <span className="text-lg font-bold text-green-600">{slaMetrics.slaCompliantOrders}</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Compliance Rate</span>
                        <span className={`text-lg font-bold ${getSLAComplianceColor(slaMetrics.complianceRate)}`}>
                          {slaMetrics.complianceRate}%
                        </span>
                      </div>
                      <Progress value={slaMetrics.complianceRate} className="h-2" />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Critical Breaches</span>
                        <span className="text-lg font-bold text-red-600">{slaMetrics.criticalSLABreaches}</span>
                      </div>
                      {slaMetrics.criticalSLABreaches > 0 && (
                        <Button variant="destructive" size="sm" className="w-full">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Review Breaches
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <span className="text-sm font-medium">Average Response Time</span>
                      <div className="text-2xl font-bold">{slaMetrics.averageResponseTime} hours</div>
                      <div className="text-sm text-gray-600">Target: 4 hours</div>
                    </div>

                    <div className="space-y-2">
                      <span className="text-sm font-medium">Average Resolution Time</span>
                      <div className="text-2xl font-bold">{slaMetrics.averageResolutionTime} hours</div>
                      <div className="text-sm text-gray-600">Target: 24 hours</div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {technicianMetrics ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Technician Performance: {technicianMetrics.technicianName}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Completion Rate</span>
                    <div className="text-2xl font-bold text-green-600">
                      {technicianMetrics.metrics.completionRate}%
                    </div>
                    <Progress value={technicianMetrics.metrics.completionRate} className="h-2" />
                  </div>

                  <div className="space-y-2">
                    <span className="text-sm font-medium">Customer Satisfaction</span>
                    <div className="text-2xl font-bold text-yellow-600">
                      {technicianMetrics.metrics.customerSatisfactionScore}%
                    </div>
                    <Progress value={technicianMetrics.metrics.customerSatisfactionScore} className="h-2" />
                  </div>

                  <div className="space-y-2">
                    <span className="text-sm font-medium">First Time Fix</span>
                    <div className="text-2xl font-bold text-blue-600">
                      {technicianMetrics.metrics.firstTimeFixRate}%
                    </div>
                    <Progress value={technicianMetrics.metrics.firstTimeFixRate} className="h-2" />
                  </div>

                  <div className="space-y-2">
                    <span className="text-sm font-medium">Revenue Generated</span>
                    <div className="text-2xl font-bold text-green-600">
                      ${technicianMetrics.metrics.revenueGenerated.toLocaleString()}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Select a technician to view detailed performance metrics</CardTitle>
              </CardHeader>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="inventory" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Inventory Optimization Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {inventoryRecommendations.map((recommendation) => (
                  <div key={recommendation.itemId} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{recommendation.name}</h4>
                        <p className="text-sm text-gray-600">Part: {recommendation.partNumber}</p>
                      </div>
                      <Badge className={getUrgencyColor(recommendation.urgency)}>
                        {recommendation.urgency}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div className="text-sm">
                        <span className="font-medium">Current Stock:</span> {recommendation.currentStock}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">Action:</span> {recommendation.recommendedAction.replace('_', ' ')}
                      </div>
                      {recommendation.recommendedQuantity && (
                        <div className="text-sm">
                          <span className="font-medium">Quantity:</span> {recommendation.recommendedQuantity}
                        </div>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-3">{recommendation.reasoning}</p>
                    
                    {recommendation.potentialSavings && recommendation.potentialSavings > 0 && (
                      <div className="text-sm text-green-600 font-medium">
                        Potential Savings: ${recommendation.potentialSavings.toLocaleString()}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
