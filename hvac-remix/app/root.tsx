// CSS bundle is deprecated in Remix v2
import type { LinksFunction, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useLocation,
  useFetcher,
} from "@remix-run/react";
import { useEffect, useState } from "react";
// Import our custom error boundary
import RootErrorBoundary from "~/root.error";
import { PWAUpdatePrompt } from "~/components/pwa-update-prompt";

import { getUser } from "~/session.server";
// Notifications now handled by GoBackend-Kratos
import stylesheet from "~/tailwind.css";
import { ThemeProvider } from "~/components/theme-provider";
import { MainLayout } from "~/components/templates/main-layout";
import { TRPCProvider } from "~/providers/trpc-provider";
import type { Notification } from "~/types/shared";

export const links: LinksFunction = () => [
  // Google Fonts
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  { rel: "preconnect", href: "https://fonts.gstatic.com", crossOrigin: "anonymous" },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap"
  },
  // App stylesheets
  { rel: "stylesheet", href: stylesheet },
  // CSS bundle deprecated in Remix v2
  // PWA manifest and icons
  { rel: "manifest", href: "/manifest.webmanifest" },
  { rel: "apple-touch-icon", href: "/logo192.png" },
  { rel: "icon", href: "/favicon.ico", type: "image/x-icon" },
  // SEO
  { rel: "canonical", href: "https://your-domain.com" },
];

export const meta: MetaFunction = () => {
  const title = "HVAC CRM Servicetool";
  const description = "Comprehensive CRM system for HVAC service companies. Manage customers, devices, service orders, and more.";
  const url = "https://your-domain.com";

  return [
    { title },
    { name: "description", content: description },
    // PWA
    { name: "theme-color", content: "#2563eb" },
    { name: "apple-mobile-web-app-capable", content: "yes" },
    { name: "apple-mobile-web-app-status-bar-style", content: "default" },
    { name: "viewport", content: "width=device-width,initial-scale=1" },
    // Open Graph
    { property: "og:type", content: "website" },
    { property: "og:url", content: url },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:image", content: `${url}/og-image.png` },
    // Twitter
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:url", content: url },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
    { name: "twitter:image", content: `${url}/twitter-image.png` },
    // Additional
    { name: "application-name", content: "HVAC CRM" },
    { name: "apple-mobile-web-app-title", content: "HVAC CRM" },
    { name: "format-detection", content: "telephone=no" },
    { name: "msapplication-TileColor", content: "#2563eb" },
    { name: "msapplication-tap-highlight", content: "no" },
  ];
};

// Export the RootErrorBoundary component
export { default as ErrorBoundary } from "~/root.error";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);

  // Pobierz nieprzeczytane powiadomienia dla zalogowanego użytkownika
  let notifications: Notification[] = [];

  if (user) {
    try {
      // TODO: Implement notifications via GoBackend-Kratos
      // notifications = await goBackendBridge.getNotifications(user.id);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      // Continue without notifications if error occurs
    }
  }

  // Add environment variables for client-side
  const ENV = {
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
  };

  return json({
    user,
    userRole: user?.role || 'USER',
    notifications,
    ENV
  });
};

export default function App() {
  const { user, userRole, notifications: initialNotifications, ENV } = useLoaderData<typeof loader>();
  const location = useLocation();
  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications || []);
  const notificationFetcher = useFetcher();

  // Make environment variables available on the window object
  useEffect(() => {
    if (typeof window !== 'undefined' && ENV) {
      window.ENV = ENV;
    }
  }, [ENV]);

  // Track page views
  useEffect(() => {
    // Import dynamically to avoid server-side import
    import('~/utils/monitoring.client').then(({ trackPageView }) => {
      trackPageView(location.pathname, document.title);
    });
  }, [location.pathname]);

  // Aktualizuj stan powiadomień, gdy zmienią się dane z loadera
  useEffect(() => {
    setNotifications(initialNotifications || []);
  }, [initialNotifications]);

  // Funkcja do oznaczania powiadomienia jako przeczytane
  const handleMarkAsRead = (id: string) => {
    notificationFetcher.submit(
      { id, action: "markAsRead" },
      { method: "post", action: "/api/notifications" }
    );

    // Optymistycznie aktualizuj UI
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, status: 'read' }
          : notification
      ).filter(notification => notification.status === 'unread')
    );
  };

  // Funkcja do oznaczania wszystkich powiadomień jako przeczytane
  const handleMarkAllAsRead = () => {
    notificationFetcher.submit(
      { action: "markAllAsRead" },
      { method: "post", action: "/api/notifications" }
    );

    // Optymistycznie aktualizuj UI
    setNotifications([]);
  };

  // Nie pokazuj layoutu na stronach logowania i rejestracji
  const isAuthPage = location.pathname === "/login" || location.pathname === "/join";

  // Sprawdź, czy ścieżka jest dla portalu klienta
  const isCustomerPortal = location.pathname.startsWith("/customer-portal");

  // Określ efektywną rolę dla nawigacji
  const effectiveRole = isCustomerPortal ? 'CUSTOMER' : userRole;

  return (
    <html lang="pl" className="h-full">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <Meta />
        <Links />
      </head>
      <body className="h-full">
        <TRPCProvider>
          <ThemeProvider defaultTheme="system" storageKey="hvac-crm-theme">
            {isAuthPage ? (
              <Outlet />
            ) : (
              <MainLayout
                user={user}
                userRole={effectiveRole}
                notifications={notifications}
                onMarkAsRead={handleMarkAsRead}
                onMarkAllAsRead={handleMarkAllAsRead}
              >
                <Outlet />
              </MainLayout>
            )}

            {/* PWA Update Prompt */}
            <PWAUpdatePrompt />
          </ThemeProvider>
        </TRPCProvider>
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}